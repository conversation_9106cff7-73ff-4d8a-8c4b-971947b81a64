@echo off
chcp 65001 >nul
title 股票量化交易软件 - 一键打包

echo.
echo ========================================
echo 股票量化交易软件 - 一键打包工具
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境
    echo 请确保已安装Python并添加到系统PATH
    pause
    exit /b 1
)

echo ✓ Python环境检查通过
echo.

echo 正在执行打包脚本...
echo.
python 完整打包脚本.py

echo.
echo 打包完成！
echo.
echo 如果打包成功，可执行文件位于：dist\股票量化交易软件.exe
echo.

pause
