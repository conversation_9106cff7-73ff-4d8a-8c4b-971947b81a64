# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 需要包含的数据文件和目录
added_files = [
    # 核心Python模块
    ('股票看图软件_增强版.py', '.'),
    ('回测系统.py', '.'),
    ('回测分析.py', '.'),
    ('策略模板.py', '.'),
    ('多股票回测系统.py', '.'),
    ('技术指标库.py', '.'),
    ('市场数据管理.py', '.'),
    ('使用者监控.py', '.'),
    ('多股票监控管理器.py', '.'),
    ('多股票监控配置.py', '.'),
    ('交易调度器.py', '.'),
    ('浏览器驱动管理.py', '.'),
    ('网页交易买入.py', '.'),
    
    # 策略示例目录
    ('策略示例', '策略示例'),
    
    # 配置文件和缓存目录
    ('user_config', 'user_config'),
    ('market_data_cache', 'market_data_cache'),
    
    # 驱动程序目录
    ('drivers', 'drivers'),
    
    # 配置文件
    ('browser_config.json', '.'),
    ('multi_stock_config.json', '.'),
    
    # 文档文件
    ('新功能说明.md', '.'),
    ('多股票监控使用说明.md', '.'),
    ('浏览器优化说明.md', '.'),
    ('浏览器连接改进说明.md', '.'),
    ('浏览器驱动说明.md', '.'),
    ('登录成功提示说明.md', '.'),
    ('登录界面状态提示说明.md', '.'),
    ('自定义策略收集功能说明.md', '.'),
    ('进程清理修复说明.md', '.'),
    ('缓存优化效果对比.md', '.'),
    
    # 批处理文件
    ('一键安装驱动.bat', '.'),
    
    # 源代码文件
    ('上传网址源代码.txt', '.'),
]

a = Analysis(
    ['登录注册.py'],  # 以登录注册.py作为主入口
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        # 基础库
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'tkinter.filedialog',
        
        # 数据处理
        'pandas',
        'numpy',
        'openpyxl',
        'xlrd',
        
        # 网络请求
        'requests',
        'urllib3',
        'certifi',
        
        # 图像处理
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        
        # 图表绘制
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends.backend_tkagg',
        'matplotlib.figure',
        'matplotlib.backends.backend_tkagg',
        
        # 股票数据
        'tushare',
        
        # 浏览器自动化
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.edge.service',
        'selenium.webdriver.edge.options',
        'selenium.webdriver.chrome.service',
        'selenium.webdriver.chrome.options',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support',
        'selenium.webdriver.support.expected_conditions',
        'webdriver_manager',
        'webdriver_manager.microsoft',
        'webdriver_manager.chrome',
        
        # 网页解析
        'lxml',
        'bs4',
        'beautifulsoup4',
        
        # 系统相关
        'datetime',
        'threading',
        'json',
        'time',
        'os',
        'sys',
        'subprocess',
        'importlib',
        'importlib.util',
        'base64',
        'io',
        'gzip',
        'pickle',
        'hashlib',
        'uuid',
        'logging',
        'warnings',
        'collections',
        'itertools',
        'functools',
        'typing',
        'abc',
        
        # 压缩和缓存
        'gzip',
        'pickle',
        'sqlite3',
        
        # 数学计算
        'math',
        'statistics',
        'random',
        
        # 文件处理
        'pathlib',
        'glob',
        'shutil',
        'tempfile',
        
        # 网络和HTTP
        'http',
        'http.client',
        'urllib',
        'urllib.parse',
        'urllib.request',
        
        # 加密和编码
        'hashlib',
        'hmac',
        'base64',
        'binascii',
        
        # 正则表达式
        're',
        
        # 配置文件处理
        'configparser',
        'yaml',
        
        # 多进程和异步
        'multiprocessing',
        'concurrent',
        'concurrent.futures',
        'asyncio',
        
        # 数据库
        'sqlite3',
        
        # 其他常用库
        'copy',
        'weakref',
        'gc',
        'platform',
        'socket',
        'ssl',
        'email',
        'email.mime',
        'email.mime.text',
        'email.mime.multipart',
        
        # 自定义模块
        '股票看图软件_增强版',
        '回测系统',
        '回测分析',
        '策略模板',
        '多股票回测系统',
        '技术指标库',
        '市场数据管理',
        '使用者监控',
        '多股票监控管理器',
        '多股票监控配置',
        '交易调度器',
        '浏览器驱动管理',
        '网页交易买入',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的大型库
        'PyQt5',
        'PyQt6',
        'PySide2',
        'PySide6',
        'torch',
        'tensorflow',
        'cv2',
        'opencv',
        'sklearn',
        'scipy.sparse.csgraph._validation',
        'IPython',
        'jupyter',
        'notebook',
        'pytest',
        'test',
        'tests',
        'unittest',
        'doctest',
        
        # 排除开发工具
        'pdb',
        'pydoc',
        'distutils',
        'setuptools',
        'pip',
        'wheel',
        
        # 排除不必要的matplotlib后端
        'matplotlib.backends.backend_qt5agg',
        'matplotlib.backends.backend_qt4agg',
        'matplotlib.backends.backend_gtk3agg',
        'matplotlib.backends.backend_wx',
        'matplotlib.backends.backend_macosx',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='股票量化交易软件',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
