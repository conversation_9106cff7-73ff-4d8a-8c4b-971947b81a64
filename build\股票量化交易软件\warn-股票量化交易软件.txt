
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named org - imported by copy (optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named urllib.urlopen - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named urllib.urlencode - imported by urllib (delayed, optional), lxml.html (delayed, optional), tushare.futures.domestic (optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), subprocess (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), netrc (delayed, conditional), getpass (delayed), http.server (delayed, optional), webbrowser (delayed), psutil (optional), _pytest._py.path (delayed), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional), gevent.subprocess (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named grp - imported by subprocess (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), _pytest._py.path (delayed), distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional), gevent.subprocess (optional), setuptools._distutils.archive_util (optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), IPython.utils.timing (optional), fsspec.asyn (conditional, optional), test.support (delayed, conditional, optional)
missing module named 'org.python' - imported by pickle (optional), xml.sax (delayed, conditional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level), numba.testing.main (optional)
missing module named fcntl - imported by subprocess (optional), xmlrpc.server (optional), tqdm.utils (delayed, optional), pty (delayed, optional), gevent.fileobject (optional), gevent.os (optional), gevent.subprocess (conditional)
missing module named termios - imported by getpass (optional), tty (top-level), IPython.core.page (delayed, optional), prompt_toolkit.input.vt100 (top-level), tqdm.utils (delayed, optional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (top-level), gevent.testing.testrunner (top-level)
missing module named multiprocessing.freeze_support - imported by multiprocessing (conditional), numba.runtests (conditional)
missing module named multiprocessing.Pool - imported by multiprocessing (delayed, conditional), scipy._lib._util (delayed, conditional)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named multiprocessing.Process - imported by multiprocessing (top-level), jupyter_client.ssh.tunnel (top-level), gevent.tests.test__issue600 (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named 'distutils._modified' - imported by setuptools._distutils.file_util (delayed)
missing module named _typeshed - imported by setuptools.glob (conditional), setuptools.compat.py311 (conditional), pkg_resources (conditional), trio._file_io (conditional), trio._path (conditional), prompt_toolkit.eventloop.inputhook (conditional), git.objects.fun (conditional), pydantic._internal._dataclasses (conditional), setuptools._distutils.dist (conditional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional)
missing module named android - imported by platformdirs.android (delayed, conditional, optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional), tqdm.cli (delayed, conditional, optional)
missing module named jaraco.text.yield_lines - imported by setuptools._vendor.jaraco.text (top-level), setuptools._entry_points (top-level), setuptools.command._requirestxt (top-level)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.sdist (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), site (delayed, optional), rlcompleter (optional), pstats (conditional, optional), dill.source (delayed, conditional, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named pyimod02_importers - imported by D:\Softwear\PYthone\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), D:\Softwear\PYthone\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named xmlrpclib - imported by defusedxml.xmlrpc (conditional)
missing module named htmlentitydefs - imported by lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named cchardet - imported by bs4.dammit (optional)
missing module named StringIO - imported by simplejson.compat (conditional, optional), six (conditional), Cython.Compiler.Annotate (optional)
missing module named html5lib.treebuilders._base - imported by html5lib.treebuilders (top-level), lxml.html._html5builder (top-level)
missing module named collections.MutableMapping - imported by collections (optional), html5lib.treebuilders.dom (optional), html5lib.treebuilders.etree_lxml (optional)
missing module named collections.Mapping - imported by collections (optional), sortedcontainers.sorteddict (optional), parso.python.tree (optional), pytz.lazy (optional), google.auth.jwt (optional), google.auth.pluggable (optional), google.auth.identity_pool (optional), html5lib._utils (optional), html5lib._trie._base (optional), gevent.contextvars (optional), gevent.tests.test__local (optional)
missing module named html5lib.XHTMLParser - imported by html5lib (optional), lxml.html.html5parser (optional)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level), html5lib._inputstream (top-level), six.moves.urllib (top-level), html5lib.filters.sanitizer (top-level)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
missing module named 'genshi.core' - imported by html5lib.treewalkers.genshi (top-level)
missing module named genshi - imported by html5lib.treewalkers.genshi (top-level)
missing module named urlparse - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named urllib2 - imported by tushare.stock.fundamental (optional), tushare.stock.trading (optional), tushare.stock.macro (optional), tushare.util.netbase (optional), tushare.stock.classifying (optional), tushare.stock.newsevent (optional), tushare.stock.reference (optional), tushare.stock.billboard (optional), tushare.internet.boxoffice (optional), tushare.internet.indexes (optional), tushare.fund.nav (optional), tushare.futures.intlfutures (optional), tushare.stock.globals (optional), tushare.futures.domestic (optional), tushare.coins.market (optional), lxml.ElementInclude (optional), lxml.html.html5parser (optional), gevent.tests.test__example_wsgiserver (optional), gevent.tests.test__greenness (optional)
missing module named lxml_html_clean - imported by lxml.html.clean (optional)
missing module named cssselect - imported by lxml.cssselect (optional)
missing module named openpyxl.tests - imported by openpyxl.reader.excel (optional)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level), trio._dtls (delayed, conditional), google.auth.transport._mtls_helper (delayed), google.auth.transport.requests (delayed, optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional), fsspec.compression (optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named collections.Callable - imported by collections (optional), socks (optional), cffi.api (optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named UserDict - imported by simplejson.ordered_dict (top-level), pytz.lazy (optional)
missing module named cStringIO - imported by simplejson.compat (conditional, optional), cffi.ffiplatform (optional), xlrd.timemachine (conditional)
missing module named sip - imported by IPython.external.qt_loaders (delayed, optional), matplotlib.backends.qt_compat (delayed, conditional)
missing module named argcomplete - imported by traitlets.config.loader (delayed, optional), traitlets.config.argcomplete_config (optional)
missing module named traitlets.config.Application - imported by traitlets.config (delayed, conditional), traitlets.log (delayed, conditional), ipykernel.kernelspec (top-level)
missing module named pygments.formatters.TerminalFormatter - imported by pygments.formatters (delayed, optional), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named pygments.formatters.Terminal256Formatter - imported by pygments.formatters (delayed, conditional, optional), numba.core.ir (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.utils (delayed, conditional, optional)
missing module named pygments.formatters.LatexFormatter - imported by pygments.formatters (delayed), IPython.lib.display (delayed)
missing module named pygments.formatters.HtmlFormatter - imported by pygments.formatters (delayed), IPython.lib.display (delayed), IPython.core.oinspect (top-level), stack_data.core (delayed), Cython.Compiler.Annotate (delayed, optional), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
missing module named pygments.lexers.LlvmLexer - imported by pygments.lexers (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.utils (delayed, conditional, optional)
missing module named pygments.lexers.GasLexer - imported by pygments.lexers (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional)
missing module named pygments.lexers.CppLexer - imported by pygments.lexers (delayed, optional), Cython.Compiler.Annotate (delayed, optional)
missing module named pygments.lexers.CythonLexer - imported by pygments.lexers (delayed, optional), Cython.Compiler.Annotate (delayed, optional)
missing module named pygments.lexers.PythonLexer - imported by pygments.lexers (top-level), IPython.core.oinspect (top-level), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named _winreg - imported by platform (delayed, optional), selenium.webdriver.firefox.firefox_binary (delayed, optional), pygments.formatters.img (optional)
missing module named pickle5 - imported by numpy.compat.py3k (optional)
missing module named _dummy_thread - imported by numpy.core.arrayprint (optional), sortedcontainers.sortedlist (conditional, optional), cffi.lock (conditional, optional)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed), scipy.linalg._decomp (top-level), scipy.optimize._optimize (top-level), scipy.interpolate._pade (top-level)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.max - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.prod - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), dill._objects (optional), scipy.stats._stats_py (top-level), scipy.sparse.linalg._isolve.utils (top-level), scipy.linalg._decomp (top-level), scipy.linalg._decomp_schur (top-level), scipy.interpolate._interpolate (top-level), scipy.interpolate._fitpack_impl (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.optimize._tnc (top-level), scipy.optimize._slsqp_py (top-level), scipy.interpolate._fitpack2 (top-level), scipy.integrate._ode (top-level), scipy._lib._finite_differences (top-level), scipy.stats._morestats (top-level), numba.cuda.vectorizers (top-level)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), dill._dill (delayed), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy.optimize._minpack_py (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.recarray - imported by numpy (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), _pytest.python_api (conditional), IPython.core.magics.namespace (delayed, conditional, optional), dill._dill (delayed), pandas.compat.numpy.function (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._mstats_basic (top-level), scipy.stats._mstats_extras (top-level), pytesseract.pytesseract (optional), seaborn._core.typing (top-level), seaborn._marks.base (top-level), seaborn._stats.density (top-level)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._decomp (top-level), scipy.linalg._decomp_ldl (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level), scipy.stats._morestats (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._matfuncs (top-level), scipy.stats._morestats (top-level)
missing module named pyodide_js - imported by threadpoolctl (delayed, optional)
missing module named numpy.lib.imag - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.real - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.iscomplexobj - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level), numpy.testing.overrides (top-level), dill._dill (delayed), dill._objects (optional)
missing module named numpy.isinf - imported by numpy (top-level), numpy.testing._private.utils (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.isnan - imported by numpy (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.isfinite - imported by numpy (top-level), numpy.testing._private.utils (top-level), scipy.linalg._decomp (top-level), scipy.linalg._matfuncs (top-level), scipy.optimize._slsqp_py (top-level)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.stats._mstats_extras (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level), dill._objects (optional), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named pexpect - imported by IPython.utils._process_posix (delayed, conditional), _pytest.pytester (conditional), _pytest.legacypath (conditional), jupyter_client.ssh.tunnel (optional)
missing module named System - imported by IPython.utils._process_cli (top-level)
missing module named clr - imported by IPython.utils._process_cli (top-level)
missing module named 'yapf.yapflib' - imported by IPython.terminal.interactiveshell (delayed)
missing module named yapf - imported by IPython.terminal.interactiveshell (delayed)
missing module named black - imported by IPython.terminal.interactiveshell (delayed)
missing module named 'jupyter_ai.completions' - imported by IPython.terminal.shortcuts.auto_suggest (delayed, optional)
missing module named jupyter_ai - imported by IPython.terminal.shortcuts.auto_suggest (delayed, optional)
missing module named jupyter_ai_magics - imported by IPython.terminal.shortcuts.auto_suggest (delayed, optional)
missing module named prompt_toolkit.filters.vi_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.document (top-level), prompt_toolkit.key_binding.bindings.page_navigation (top-level), prompt_toolkit.widgets.toolbars (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named 'prompt_toolkit.key_binding.key_bindings.vi' - imported by prompt_toolkit.key_binding.vi_state (conditional)
missing module named 'backports.functools_lru_cache' - imported by wcwidth.wcwidth (optional)
missing module named prompt_toolkit.filters.is_done - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.menus (top-level), prompt_toolkit.widgets.base (top-level), prompt_toolkit.shortcuts.progress_bar.base (top-level), prompt_toolkit.shortcuts.prompt (top-level)
missing module named prompt_toolkit.filters.has_completions - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.menus (top-level), prompt_toolkit.widgets.toolbars (top-level), prompt_toolkit.widgets.dialogs (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.vi_insert_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.containers (top-level), prompt_toolkit.key_binding.bindings.basic (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.emacs_insert_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.containers (top-level), prompt_toolkit.key_binding.bindings.basic (top-level), prompt_toolkit.key_binding.bindings.emacs (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.is_searching - imported by prompt_toolkit.filters (top-level), prompt_toolkit.search (top-level), prompt_toolkit.key_binding.bindings.search (top-level), prompt_toolkit.key_binding.bindings.vi (top-level)
missing module named prompt_toolkit.filters.vi_insert_multiple_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.processors (top-level)
missing module named exceptiongroup - imported by _pytest.runner (conditional), _pytest.fixtures (conditional), _pytest._code.code (conditional), trio._util (conditional), trio._core._run (conditional), trio.testing._check_streams (conditional), trio.testing._raises_group (conditional), trio._channel (conditional), trio._highlevel_open_tcp_listeners (conditional), trio._highlevel_open_tcp_stream (conditional), _pytest.unittest (conditional)
missing module named 'curio.meta' - imported by sniffio._impl (delayed, conditional)
missing module named annotationlib - imported by attr._compat (conditional)
missing module named zope.schema - imported by zope (optional), gevent._interfaces (optional)
missing module named 'twisted.trial' - imported by _pytest.unittest (delayed, conditional)
missing module named twisted - imported by _pytest.unittest (conditional)
missing module named 'argcomplete.completers' - imported by _pytest._argcomplete (conditional, optional)
missing module named dummy_thread - imported by sortedcontainers.sortedlist (conditional, optional), cffi.lock (conditional, optional)
missing module named thread - imported by sortedcontainers.sortedlist (conditional, optional), cffi.lock (conditional, optional), cffi.cparser (conditional, optional), gevent.tests.lock_tests (optional), gevent.tests.test__core_async (optional), gevent.tests.test__refcount (optional), gevent.tests.test__thread (optional)
missing module named cPickle - imported by IPython.external.pickleshare (optional), pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named 'hypothesis.internal' - imported by trio._core._run (delayed, optional)
missing module named hypothesis - imported by trio._core._run (delayed)
missing module named collections.Sequence - imported by collections (optional), sortedcontainers.sortedlist (optional), sortedcontainers.sortedset (optional), sortedcontainers.sorteddict (optional)
missing module named collections.ValuesView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.KeysView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.ItemsView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.Set - imported by collections (optional), sortedcontainers.sortedset (optional)
missing module named collections.MutableSet - imported by collections (optional), sortedcontainers.sortedset (optional)
missing module named collections.MutableSequence - imported by collections (optional), sortedcontainers.sortedlist (optional)
missing module named curio - imported by IPython.core.async_helpers (delayed)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level)
missing module named nbformat - imported by IPython.core.magics.basic (delayed), IPython.core.interactiveshell (delayed, conditional)
missing module named sqlite3.OperationalError - imported by sqlite3 (optional), IPython.core.history (optional)
missing module named sqlite3.DatabaseError - imported by sqlite3 (optional), IPython.core.history (optional)
missing module named 'IPython.config' - imported by IPython.core.history (conditional)
missing module named 'astroid.node_classes' - imported by asttokens.astroid_compat (optional)
missing module named 'astroid.nodes' - imported by asttokens.astroid_compat (optional)
missing module named astroid - imported by asttokens.astroid_compat (optional), asttokens.util (optional)
missing module named numpydoc - imported by jedi.inference.docstrings (delayed)
missing module named netifaces - imported by jupyter_client.localinterfaces (delayed)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named _continuation - imported by gevent.greenlet (conditional)
missing module named httplib - imported by tushare.futures.domestic (optional), gevent.tests.test__socket_ssl (optional)
missing module named selectors2 - imported by gevent.selectors (optional), gevent.tests.test__monkey_selectors (optional)
missing module named _import_wait - imported by gevent.tests.test__import_wait (optional)
missing module named _blocks_at_top_level - imported by gevent.tests.test__import_blocking_in_greenlet (delayed, optional)
missing module named SimpleHTTPServer - imported by gevent.tests.test__greenness (optional)
missing module named BaseHTTPServer - imported by gevent.tests.test__greenness (optional)
missing module named getaddrinfo_module - imported by gevent.tests.test__getaddrinfo_import (optional)
missing module named dns - imported by gevent.tests.monkey_package.issue1526_no_monkey (top-level), gevent.tests.monkey_package.issue1526_with_monkey (top-level)
missing module named 'dns.rdtypes' - imported by gevent.tests.monkey_package.issue1526_no_monkey (top-level)
missing module named __builtin__ - imported by Cython.Shadow (optional), Cython.Compiler.Errors (optional), Cython.Compiler.Main (optional), Cython.Compiler.Symtab (optional), Cython.Compiler.ExprNodes (optional), Cython.Compiler.Optimize (optional), Cython.Distutils.build_ext (optional), Cython.Build.Inline (delayed, optional), gevent.backdoor (delayed, optional), gevent.libev.corecffi (conditional), gevent.testing.six (conditional)
missing module named test.libregrtest.ALL_RESOURCES - imported by test.libregrtest (delayed, optional), gevent.testing.resources (delayed, optional)
missing module named objgraph - imported by gevent.testing.leakcheck (optional)
missing module named mock - imported by gevent.testing (optional)
missing module named mimetools - imported by gevent.pywsgi (optional)
missing module named _setuputils - imported by gevent.libev._corecffi_build (optional), gevent.libuv._corecffi_build (optional)
missing module named gevent.libev._corecffi - imported by gevent.libev (top-level), gevent.libev.corecffi (top-level), gevent.libev.watcher (top-level)
missing module named _setuplibev - imported by gevent.libev._corecffi_build (optional)
missing module named zmq.backend.zmq_version_info - imported by zmq.backend (top-level), zmq.sugar.version (top-level)
missing module named zmq.backend.Frame - imported by zmq.backend (top-level), zmq.sugar.frame (top-level), zmq.sugar.tracker (top-level)
missing module named zmq.backend.Socket - imported by zmq.backend (top-level), zmq.sugar.socket (top-level)
missing module named zmq.backend.zmq_poll - imported by zmq.backend (top-level), zmq.sugar.poll (top-level)
missing module named pyczmq - imported by zmq.sugar.context (delayed)
missing module named zmq.backend.Context - imported by zmq.backend (top-level), zmq.sugar.context (top-level)
missing module named zmq.backend.proxy - imported by zmq.backend (top-level), zmq.sugar (top-level)
missing module named zmq.ZMQError - imported by zmq (delayed, optional), zmq.sugar.attrsettr (delayed, optional)
missing module named zmq.backend.zmq_errno - imported by zmq.backend (delayed), zmq.error (delayed, conditional)
missing module named zmq.backend.strerror - imported by zmq.backend (delayed), zmq.error (delayed)
missing module named zmq.zmq_version_info - imported by zmq (delayed, conditional), zmq.error (delayed, conditional)
missing module named zmq.zmq_version - imported by zmq (delayed, conditional), zmq.error (delayed, conditional)
missing module named _subprocess - imported by jupyter_client.launcher (delayed, conditional, optional), ipykernel.parentpoller (delayed, optional)
missing module named paramiko - imported by jupyter_client.ssh.tunnel (optional), fsspec.implementations.sftp (top-level)
missing module named _dbm - imported by dbm.ndbm (top-level)
missing module named _gdbm - imported by dbm.gnu (top-level)
missing module named diff - imported by dill._dill (delayed, conditional, optional)
missing module named dill.diff - imported by dill (delayed, conditional, optional), dill._dill (delayed, conditional, optional)
missing module named version - imported by dill (optional)
missing module named 'ipyparallel.serialize' - imported by ipykernel.ipkernel (delayed, optional), ipykernel.serialize (optional), ipykernel.pickleutil (optional)
missing module named ipykernel.get_connection_info - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.get_connection_file - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.connect_qtconsole - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
excluded module named PySide6 - imported by ipykernel.eventloops (delayed, conditional, optional)
excluded module named PyQt6 - imported by ipykernel.eventloops (delayed, conditional, optional)
excluded module named PySide2 - imported by ipykernel.eventloops (delayed, conditional, optional)
excluded module named PyQt5 - imported by ipykernel.eventloops (delayed, conditional, optional), matplotlib.backends.qt_compat (delayed, conditional)
missing module named 'gi.repository' - imported by ipykernel.gui.gtk3embed (top-level)
missing module named gi - imported by matplotlib.cbook (delayed, conditional), ipykernel.gui.gtk3embed (top-level)
missing module named gtk - imported by ipykernel.gui.gtkembed (top-level)
missing module named gobject - imported by ipykernel.gui.gtkembed (top-level)
missing module named wx - imported by ipykernel.eventloops (delayed), IPython.lib.guisupport (delayed)
missing module named ipyparallel - imported by ipykernel.zmqshell (delayed, conditional)
missing module named appnope - imported by ipykernel.ipkernel (delayed, conditional)
missing module named '_pydevd_bundle.pydevd_api' - imported by ipykernel.debugger (delayed)
missing module named '_pydevd_bundle.pydevd_suspended_frames' - imported by ipykernel.debugger (optional)
missing module named _pydevd_bundle - imported by debugpy._vendored.force_pydevd (top-level), ipykernel.debugger (optional)
missing module named pydevd_file_utils - imported by debugpy.server.api (top-level)
missing module named '_pydevd_bundle.pydevd_constants' - imported by debugpy.server.api (top-level)
missing module named pydevd - imported by debugpy._vendored.force_pydevd (top-level), debugpy.server.api (top-level)
missing module named markupsafe._speedups - imported by markupsafe (optional)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named contourpy._contourpy - imported by contourpy (conditional)
missing module named 'contourpy._contourpy' - imported by contourpy (top-level), contourpy.convert (top-level), contourpy.enum_util (top-level), contourpy.typecheck (conditional), contourpy.array (conditional), contourpy.dechunk (top-level)
missing module named shiboken2 - imported by matplotlib.backends.qt_compat (delayed, conditional, optional)
missing module named docrepr - imported by IPython.core.interactiveshell (optional)
missing module named pathlib2 - imported by IPython.external.pickleshare (optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional), rich.live (delayed, conditional, optional), seaborn.widgets (optional)
missing module named setuptools_scm - imported by matplotlib (delayed, conditional, optional), pyarrow (optional), tqdm.version (optional)
missing module named 'sphinx.ext' - imported by pyarrow.vendored.docscrape (delayed, conditional), seaborn.external.docscrape (delayed, conditional)
missing module named 'pandas.api.internals' - imported by pyarrow.pandas_compat (delayed, conditional)
missing module named 'pyarrow._cuda' - imported by pyarrow.cuda (top-level)
missing module named 'pyarrow.gandiva' - imported by pyarrow.conftest (optional)
missing module named fastparquet - imported by fsspec.parquet (delayed), pyarrow.conftest (optional)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named pythran - imported by Cython.Build.Dependencies (optional), Cython.Compiler.Pythran (optional)
missing module named pyximport.test - imported by pyximport (conditional), pyximport.pyxbuild (conditional)
missing module named collections.Iterable - imported by collections (optional), Cython.Build.Dependencies (optional)
missing module named Cython.Parser - imported by Cython.Compiler.Main (delayed, conditional, optional)
missing module named distributed - imported by fsspec.transaction (delayed)
missing module named requests_kerberos - imported by fsspec.implementations.webhdfs (delayed, conditional)
missing module named smbprotocol - imported by fsspec.implementations.smb (top-level)
missing module named smbclient - imported by fsspec.implementations.smb (top-level)
missing module named kerchunk - imported by fsspec.implementations.reference (delayed)
missing module named ujson - imported by fsspec.implementations.cache_metadata (optional), fsspec.implementations.reference (optional)
missing module named 'libarchive.ffi' - imported by fsspec.implementations.libarchive (top-level)
missing module named libarchive - imported by fsspec.implementations.libarchive (top-level)
missing module named uvloop - imported by aiohttp.worker (delayed)
missing module named async_timeout - imported by aiohttp.helpers (conditional), aiohttp.web_ws (conditional), aiohttp.client_ws (conditional)
missing module named 'gunicorn.workers' - imported by aiohttp.worker (top-level)
missing module named gunicorn - imported by aiohttp.worker (top-level)
missing module named aiodns - imported by aiohttp.resolver (optional)
missing module named pygit2 - imported by fsspec.implementations.git (top-level)
missing module named 'distributed.worker' - imported by fsspec.implementations.dask (top-level)
missing module named 'distributed.client' - imported by fsspec.implementations.dask (top-level)
missing module named dask - imported by scipy._lib.array_api_compat.common._helpers (delayed), fsspec.implementations.dask (top-level)
missing module named panel - imported by fsspec.gui (top-level)
missing module named fuse - imported by fsspec.fuse (top-level)
missing module named lz4 - imported by fsspec.compression (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named 'pyarrow._azurefs' - imported by pyarrow.fs (optional)
missing module named 'setuptools_scm.git' - imported by pyarrow (delayed, optional)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named 'sqlalchemy.engine' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.sql' - imported by pandas.io.sql (conditional)
missing module named sqlalchemy - imported by pandas.io.sql (delayed, conditional)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named 'pyu2f.model' - imported by google.oauth2.challenges (delayed, optional)
missing module named 'pyu2f.errors' - imported by google.oauth2.challenges (delayed, optional)
missing module named pyu2f - imported by google.oauth2.challenges (delayed, optional)
missing module named 'requests.packages.urllib3' - imported by google.auth.transport.requests (top-level)
missing module named 'google.appengine' - imported by google.auth.app_engine (optional)
missing module named xlsxwriter - imported by pandas.io.excel._xlsxwriter (delayed)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named numba.typed.Dict - imported by numba.typed (delayed), numba.core.typing.typeof (delayed), numba.typed.dictobject (delayed, conditional), numba.typed.dictimpl (delayed), pandas.core._numba.extensions (delayed)
missing module named llvmlite.ir.Constant - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed), numba.core.base (top-level), numba.core.pythonapi (top-level), numba.core.lowering (top-level), numba.core.generators (top-level), numba.core.callwrapper (top-level), numba.pycc.llvm_types (top-level), numba.np.npdatetime (top-level), numba.np.math.mathimpl (top-level), numba.np.math.numbers (top-level), numba.cpython.unicode (top-level), numba.np.arrayobj (top-level), numba.np.ufunc.wrappers (top-level), numba.cpython.old_mathimpl (top-level), numba.cpython.old_numbers (top-level), numba.cpython.new_mathimpl (top-level), numba.cpython.new_numbers (top-level)
missing module named llvmlite.binding.parse_assembly - imported by llvmlite.binding (delayed), llvmlite.ir.types (delayed)
missing module named graphviz - imported by llvmlite.binding.analysis (delayed), numba.core.ir (delayed, optional), numba.core.controlflow (delayed, optional), numba.misc.inspection (delayed, optional), numba.core.codegen (delayed)
missing module named llvmlite.ir.Value - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.GlobalVariable - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Module - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named numba.core.types.StringLiteral - imported by numba.core.types (top-level), numba.np.arrayobj (top-level)
missing module named numba.core.types.Tuple - imported by numba.core.types (delayed), numba.core.types.iterators (delayed), numba.core.types.npytypes (delayed)
missing module named numba.core.types.Array - imported by numba.core.types (delayed), numba.core.types.abstract (delayed)
missing module named numba.core.types.WrapperAddressProtocol - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionPrototype - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.UndefinedFunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.ClassInstanceType - imported by numba.core.types (top-level), numba.experimental.jitclass.overloads (top-level)
missing module named coverage - imported by numba.misc.coverage_support (optional), numba.tests.support (optional)
missing module named 'elftools.elf' - imported by numba.core.codegen (delayed)
missing module named elftools - imported by numba.core.codegen (delayed)
missing module named r2pipe - imported by numba.misc.inspection (delayed, optional)
missing module named 'com.sun' - imported by numba.misc.appdirs (delayed, conditional, optional), seaborn.external.appdirs (delayed, conditional, optional)
missing module named com - imported by numba.misc.appdirs (delayed)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional)
missing module named numba.types.uint64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level)
missing module named numba.types.int64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float32 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named ptxcompiler - imported by numba.cuda.cudadrv.driver (delayed, optional), numba.cuda.testing (delayed, optional)
missing module named cubinlinker - imported by numba.cuda.cudadrv.driver (delayed, optional), numba.cuda.testing (delayed, optional)
missing module named cuda - imported by numba.core.config (delayed, conditional, optional), numba.cuda.cudadrv.driver (conditional)
missing module named numba.types.Tuple - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.void - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int32 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int16 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named 'yaml._yaml' - imported by yaml.cyaml (top-level)
missing module named xmlrunner - imported by numba.testing (delayed, conditional)
missing module named gitdb_speedups - imported by gitdb.fun (optional)
missing module named 'gitdb_speedups._perf' - imported by gitdb.stream (optional), gitdb.pack (optional)
missing module named sha - imported by gitdb.util (delayed, optional)
missing module named Queue - imported by numba.testing.main (optional)
missing module named numba.cuda.is_available - imported by numba.cuda (delayed), numba.cuda.testing (delayed)
missing module named numba.core.types.NoneType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.Type - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.ListTypeIteratorType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListTypeIterableType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListType - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.typedlist (top-level)
missing module named numba.core.types.DictIteratorType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictValuesIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictKeysIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictItemsIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictType - imported by numba.core.types (top-level), numba.typed.typeddict (top-level), numba.typed.dictobject (top-level)
missing module named numba.typed.List - imported by numba.typed (delayed), numba.core.typing.typeof (delayed), numba.core.codegen (delayed), numba.typed.listobject (delayed)
missing module named cupy_backends - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'cupy.cuda' - imported by scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'jax.experimental' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'jax.numpy' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'dask.array' - imported by scipy._lib.array_api_compat.dask.array (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named numpy.complex128 - imported by numpy (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.complex64 - imported by numpy (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named sparse - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional)
missing module named ndonnx - imported by scipy._lib.array_api_compat.common._helpers (delayed)
excluded module named torch - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.torch (top-level), scipy._lib.array_api_compat.torch._info (top-level), scipy._lib.array_api_compat.torch._aliases (top-level), scipy._lib._array_api (delayed, conditional)
missing module named cupy - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.cupy (top-level), scipy._lib.array_api_compat.cupy._aliases (top-level), scipy._lib.array_api_compat.cupy._info (top-level), scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib._array_api (delayed, conditional)
missing module named jax - imported by scipy._lib.array_api_compat.common._helpers (delayed), scipy._lib._array_api (delayed, conditional)
missing module named 'numpy.lib.array_utils' - imported by scipy._lib.array_api_compat.common._linalg (conditional)
missing module named 'numpy.linalg._linalg' - imported by scipy._lib.array_api_compat.numpy.linalg (delayed, optional)
missing module named sphinx - imported by scipy._lib._docscrape (delayed, conditional)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named cupyx - imported by scipy._lib._array_api (delayed, conditional)
missing module named scipy.special.gammaincinv - imported by scipy.special (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.fft._fftlog_backend (top-level), scipy.stats._multivariate (top-level)
missing module named 'scipy.optimize._highspy._core.simplex_constants' - imported by scipy.optimize._linprog_highs (top-level)
missing module named scipy.spatial.Voronoi - imported by scipy.spatial (top-level), scipy.stats._qmc (top-level)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named numpy.power - imported by numpy (top-level), scipy.stats._kde (top-level), seaborn.external.kde (top-level)
missing module named numpy.logical_and - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.floor - imported by numpy (top-level), scipy.special._basic (top-level), scipy.special._orthogonal (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.log - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._morestats (top-level)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named numpy.sinh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.cosh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.tanh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.expm1 - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.log1p - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.ceil - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named 'scikits.umfpack' - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named numpy.inexact - imported by numpy (top-level), scipy.linalg._decomp (top-level), scipy.special._basic (top-level), scipy.optimize._minpack_py (top-level)
missing module named numpy.greater - imported by numpy (top-level), scipy.optimize._minpack_py (top-level)
missing module named numpy.arccos - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level), scipy.special._orthogonal (top-level)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named numpy.sign - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.conjugate - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.logical_not - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.single - imported by numpy (top-level), scipy.linalg._decomp_schur (top-level)
missing module named numpy.conj - imported by numpy (top-level), scipy.linalg._decomp (top-level)
missing module named numpy.arcsin - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level)
missing module named scikits - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named scipy.sparse.linalg.onenormest - imported by scipy.sparse.linalg (top-level), scipy.linalg._matfuncs_inv_ssq (top-level)
missing module named scipy.sparse.diags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.spdiags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.dia_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.kron - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.eye - imported by scipy.sparse (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level), scipy.optimize._trustregion_constr.equality_constrained_sqp (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.sparse.diags_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.eye_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.csc_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.optimize._milp (top-level)
missing module named scipy.sparse.csr_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._ndbspline (top-level)
missing module named scipy.sparse.SparseEfficiencyWarning - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.csc_matrix - imported by scipy.sparse (top-level), scipy.linalg._sketches (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level), scipy.optimize._linprog_highs (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.sparse.issparse - imported by scipy.sparse (delayed), scipy._lib._array_api (delayed), scipy.sparse.linalg._interface (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._norm (top-level), scipy.sparse.csgraph._laplacian (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._constraints (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._differentialevolution (top-level), scipy.optimize._milp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), pandas.core.dtypes.common (delayed, conditional, optional), scipy.sparse.csgraph._validation (top-level)
missing module named scipy._distributor_init_local - imported by scipy (optional), scipy._distributor_init (optional)
missing module named pandas.compat.StringIO - imported by pandas.compat (conditional), tushare.stock.fundamental (conditional), tushare.stock.trading (conditional), tushare.stock.reference (conditional), tushare.stock.shibor (conditional), tushare.stock.billboard (conditional), tushare.internet.indexes (conditional)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
missing module named 'pytdx.exhq' - imported by tushare.util.conns (delayed)
missing module named pytdx - imported by tushare.util.conns (delayed)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic._internal._core_utils (delayed), pydantic.deprecated.copy_internals (delayed, conditional), tushare.subs.model.tick (top-level)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named linkify_it - imported by markdown_it.main (optional)
missing module named email_validator - imported by pydantic.networks (delayed, conditional, optional), pydantic.v1.networks (delayed, conditional, optional)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional, optional), google.protobuf.internal.api_implementation (conditional, optional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (conditional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named wsaccel - imported by websocket._utils (optional)
missing module named 'python_socks._types' - imported by websocket._http (optional)
missing module named 'python_socks._errors' - imported by websocket._http (optional)
missing module named python_socks - imported by websocket._http (optional)
missing module named 'wsaccel.xormask' - imported by websocket._abnf (optional)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named fastcluster - imported by seaborn.matrix (delayed)
missing module named 'statsmodels.nonparametric' - imported by seaborn.regression (delayed)
missing module named 'statsmodels.tools' - imported by seaborn.regression (delayed)
missing module named 'statsmodels.robust' - imported by seaborn.regression (delayed, conditional)
missing module named 'statsmodels.genmod' - imported by seaborn.regression (delayed, conditional)
missing module named statsmodels - imported by seaborn.regression (optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
