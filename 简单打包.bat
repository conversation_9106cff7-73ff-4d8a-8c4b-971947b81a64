@echo off
chcp 65001 >nul
title 股票量化交易软件 - 简单打包

echo.
echo ========================================
echo 股票量化交易软件 - 简单打包工具
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境
    echo 请确保已安装Python并添加到系统PATH
    pause
    exit /b 1
)

echo ✓ Python环境检查通过
echo.

echo 正在检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo PyInstaller未安装，正在安装...
    python -m pip install pyinstaller
    if errorlevel 1 (
        echo PyInstaller安装失败
        pause
        exit /b 1
    )
    echo ✓ PyInstaller安装成功
) else (
    echo ✓ PyInstaller已安装
)

echo.
echo 正在清理之前的构建文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist *.spec del *.spec
echo ✓ 清理完成

echo.
echo 正在开始打包...
echo 这可能需要几分钟时间，请耐心等待...
echo.

pyinstaller --onefile --noconsole --name="股票量化交易软件" ^
    --add-data="股票看图软件_增强版.py;." ^
    --add-data="回测系统.py;." ^
    --add-data="回测分析.py;." ^
    --add-data="策略模板.py;." ^
    --add-data="多股票回测系统.py;." ^
    --add-data="技术指标库.py;." ^
    --add-data="市场数据管理.py;." ^
    --add-data="使用者监控.py;." ^
    --add-data="多股票监控管理器.py;." ^
    --add-data="多股票监控配置.py;." ^
    --add-data="交易调度器.py;." ^
    --add-data="浏览器驱动管理.py;." ^
    --add-data="网页交易买入.py;." ^
    --add-data="策略示例;策略示例" ^
    --add-data="user_config;user_config" ^
    --add-data="market_data_cache;market_data_cache" ^
    --add-data="drivers;drivers" ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=tkinter.messagebox ^
    --hidden-import=tkinter.scrolledtext ^
    --hidden-import=tkinter.filedialog ^
    --hidden-import=pandas ^
    --hidden-import=numpy ^
    --hidden-import=matplotlib ^
    --hidden-import=matplotlib.pyplot ^
    --hidden-import=matplotlib.backends.backend_tkagg ^
    --hidden-import=tushare ^
    --hidden-import=selenium ^
    --hidden-import=selenium.webdriver ^
    --hidden-import=requests ^
    --hidden-import=PIL ^
    --hidden-import=openpyxl ^
    --hidden-import=lxml ^
    --hidden-import=bs4 ^
    --hidden-import=webdriver_manager ^
    --hidden-import=webdriver_manager.microsoft ^
    --hidden-import=webdriver_manager.chrome ^
    登录注册.py

if errorlevel 1 (
    echo.
    echo ✗ 打包失败
    pause
    exit /b 1
)

echo.
echo ✓ 打包成功！
echo.

if exist "dist\股票量化交易软件.exe" (
    echo ✓ 生成的exe文件: dist\股票量化交易软件.exe
    
    for %%I in ("dist\股票量化交易软件.exe") do (
        set /a size=%%~zI/1024/1024
        echo ✓ 文件大小: !size! MB
    )
    
    echo.
    echo 打包完成！可执行文件位于 dist 目录中
) else (
    echo ✗ 未找到生成的exe文件
)

echo.
pause
