#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整打包脚本 - 将股票量化交易软件打包成单个exe文件
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller 已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("✗ PyInstaller 未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装 PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ PyInstaller 安装失败: {e}")
        return False

def check_dependencies():
    """检查必要的依赖库"""
    required_packages = [
        'tkinter',
        'pandas',
        'numpy',
        'matplotlib',
        'tushare',
        'selenium',
        'requests',
        'PIL',
        'openpyxl',
        'lxml',
        'bs4'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'PIL':
                from PIL import Image
            elif package == 'bs4':
                import bs4
            else:
                __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - 缺失")
            missing_packages.append(package)
    
    return missing_packages

def install_missing_packages(packages):
    """安装缺失的包"""
    if not packages:
        return True
    
    print(f"\n正在安装缺失的包: {', '.join(packages)}")
    
    # 特殊处理某些包名
    install_map = {
        'PIL': 'Pillow',
        'bs4': 'beautifulsoup4'
    }
    
    for package in packages:
        install_name = install_map.get(package, package)
        try:
            print(f"安装 {install_name}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", install_name])
            print(f"✓ {install_name} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"✗ {install_name} 安装失败: {e}")
            return False
    
    return True

def check_required_files():
    """检查必要的文件是否存在"""
    required_files = [
        '登录注册.py',
        '股票看图软件_增强版.py',
        '回测系统.py',
        '回测分析.py',
        '策略模板.py',
        '多股票回测系统.py',
        '技术指标库.py',
        '完整打包配置.spec'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file} - 缺失")
            missing_files.append(file)
    
    return missing_files

def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目录: {dir_name}")
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 已清理 {dir_name}")
            except Exception as e:
                print(f"✗ 清理 {dir_name} 失败: {e}")

def run_pyinstaller():
    """运行PyInstaller进行打包"""
    spec_file = '完整打包配置.spec'
    
    if not os.path.exists(spec_file):
        print(f"✗ 配置文件 {spec_file} 不存在")
        return False
    
    print(f"\n开始打包，使用配置文件: {spec_file}")
    print("这可能需要几分钟时间，请耐心等待...")
    
    try:
        # 运行PyInstaller
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", spec_file]
        print(f"执行命令: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            encoding='utf-8',
            errors='ignore'
        )
        
        # 实时显示输出
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        return_code = process.poll()
        
        if return_code == 0:
            print("\n✓ 打包成功！")
            return True
        else:
            print(f"\n✗ 打包失败，返回码: {return_code}")
            return False
            
    except Exception as e:
        print(f"✗ 打包过程中出现错误: {e}")
        return False

def check_output():
    """检查输出文件"""
    exe_path = os.path.join('dist', '股票量化交易软件.exe')
    
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path)
        file_size_mb = file_size / (1024 * 1024)
        print(f"✓ 生成的exe文件: {exe_path}")
        print(f"✓ 文件大小: {file_size_mb:.1f} MB")
        return True
    else:
        print(f"✗ 未找到输出文件: {exe_path}")
        return False

def create_readme():
    """创建使用说明文件"""
    readme_content = """# 股票量化交易软件

## 软件介绍
这是一个集成了登录注册和股票分析功能的量化交易软件。

## 使用方法
1. 双击 "股票量化交易软件.exe" 启动程序
2. 首次使用需要注册Tushare账号或使用现有账号登录
3. 登录成功后会自动跳转到股票看图软件界面
4. 在股票软件中可以进行技术分析、回测等操作

## 功能特点
- 自动化登录注册界面
- 完整的股票技术分析功能
- 多种回测策略
- 实时数据监控
- 网页交易功能
- 多股票监控

## 注意事项
- 首次运行可能需要较长时间初始化
- 需要网络连接获取股票数据
- 建议在Windows 10或更高版本系统上运行

## 技术支持
如有问题请联系技术支持。

生成时间: {time}
""".format(time=time.strftime('%Y-%m-%d %H:%M:%S'))

    readme_path = os.path.join('dist', 'README.txt')
    try:
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"✓ 已创建使用说明: {readme_path}")
    except Exception as e:
        print(f"✗ 创建使用说明失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("股票量化交易软件 - 完整打包脚本")
    print("=" * 60)
    
    # 1. 检查PyInstaller
    print("\n1. 检查PyInstaller...")
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("无法安装PyInstaller，打包终止")
            return False
    
    # 2. 检查依赖库
    print("\n2. 检查依赖库...")
    missing_packages = check_dependencies()
    if missing_packages:
        if not install_missing_packages(missing_packages):
            print("无法安装所有依赖库，打包可能失败")
    
    # 3. 检查必要文件
    print("\n3. 检查必要文件...")
    missing_files = check_required_files()
    if missing_files:
        print(f"缺失必要文件: {', '.join(missing_files)}")
        print("请确保所有必要文件都在当前目录中")
        return False
    
    # 4. 清理构建目录
    print("\n4. 清理构建目录...")
    clean_build_dirs()
    
    # 5. 开始打包
    print("\n5. 开始打包...")
    if not run_pyinstaller():
        print("打包失败")
        return False
    
    # 6. 检查输出
    print("\n6. 检查输出...")
    if not check_output():
        print("输出文件检查失败")
        return False
    
    # 7. 创建说明文件
    print("\n7. 创建使用说明...")
    create_readme()
    
    print("\n" + "=" * 60)
    print("✓ 打包完成！")
    print("✓ 输出目录: dist/")
    print("✓ 可执行文件: dist/股票量化交易软件.exe")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按回车键退出...")
        else:
            input("\n打包失败，按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户中断打包过程")
    except Exception as e:
        print(f"\n打包过程中出现未预期的错误: {e}")
        input("按回车键退出...")
