#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速打包脚本 - 使用PyInstaller打包股票量化交易软件
"""

import os
import sys
import subprocess
import shutil

def main():
    print("=" * 50)
    print("股票量化交易软件 - 快速打包")
    print("=" * 50)
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✓ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("正在安装 PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller 安装完成")
    
    # 清理旧文件
    print("\n清理旧文件...")
    for dir_name in ['build', 'dist']:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 已清理 {dir_name}")
    
    # 构建PyInstaller命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",  # 单文件打包
        "--noconsole",  # 不显示控制台
        "--name=股票量化交易软件",

        # 排除不需要的包
        "--exclude-module=PyQt5",
        "--exclude-module=PyQt6",
        "--exclude-module=PySide2",
        "--exclude-module=PySide6",
        "--exclude-module=torch",
        "--exclude-module=tensorflow",
        "--exclude-module=cv2",
        "--exclude-module=sklearn",

        # 添加数据文件
        "--add-data=股票看图软件_增强版.py;.",
        "--add-data=回测系统.py;.",
        "--add-data=回测分析.py;.",
        "--add-data=策略模板.py;.",
        "--add-data=多股票回测系统.py;.",
        "--add-data=技术指标库.py;.",

        # 添加隐藏导入
        "--hidden-import=tkinter",
        "--hidden-import=pandas",
        "--hidden-import=numpy",
        "--hidden-import=matplotlib",
        "--hidden-import=tushare",
        "--hidden-import=selenium",
        "--hidden-import=requests",
        "--hidden-import=PIL",
        "--hidden-import=openpyxl",

        # 主文件
        "登录注册.py"
    ]
    
    print("\n开始打包...")
    print("这可能需要几分钟时间，请耐心等待...")
    
    try:
        # 运行打包命令
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            print("\n✓ 打包成功！")
            
            # 检查输出文件
            exe_path = os.path.join('dist', '股票量化交易软件.exe')
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)
                print(f"✓ 输出文件: {exe_path}")
                print(f"✓ 文件大小: {file_size:.1f} MB")
            else:
                print("✗ 未找到输出文件")
        else:
            print(f"\n✗ 打包失败")
            print("错误信息:")
            print(result.stderr)
            
    except Exception as e:
        print(f"\n✗ 打包过程出错: {e}")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
    input("按回车键退出...")
