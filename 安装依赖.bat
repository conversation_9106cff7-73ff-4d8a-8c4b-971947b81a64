@echo off
chcp 65001 >nul
title 安装股票量化交易软件依赖

echo.
echo ========================================
echo 股票量化交易软件 - 依赖安装工具
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境
    echo 请确保已安装Python并添加到系统PATH
    pause
    exit /b 1
)

echo ✓ Python环境检查通过
echo.

echo 正在升级pip...
python -m pip install --upgrade pip

echo.
echo 正在安装依赖包...
echo 这可能需要几分钟时间，请耐心等待...
echo.

python -m pip install -r requirements.txt

echo.
echo 依赖安装完成！
echo.
echo 现在可以运行 "一键打包.bat" 进行软件打包
echo.

pause
